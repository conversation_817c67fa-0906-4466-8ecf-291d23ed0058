<template>
  <div class="kpi-dashboard">
    <!-- Header Section -->
    <c-card class="dashboard-header mb-4">
      <c-card-body class="py-3">
        <div class="d-flex justify-content-between align-items-center">
          <div class="d-flex align-items-center">
            <c-icon name="cil-chart-line" class="text-primary me-2" size="xl"></c-icon>
            <h2 class="mb-0 text-primary fw-bold">KPI Dashboard</h2>
          </div>
          <div class="d-flex align-items-center">
            <span class="badge bg-success me-2">Live Data</span>
            <small class="text-muted">Last updated: {{ lastUpdated }}</small>
          </div>
        </div>
      </c-card-body>
    </c-card>

    <!-- Enhanced Filter Section -->
    <c-card class="filter-card mb-4 shadow-sm">
      <c-card-header class="bg-gradient-primary text-white">
        <div class="d-flex justify-content-between align-items-center">
          <div class="d-flex align-items-center">
            <c-icon name="cil-filter" class="me-2"></c-icon>
            <h5 class="mb-0">Filters & Controls</h5>
          </div>
          <c-button
            color="light"
            size="sm"
            @click="toggleFilters"
            class="text-primary"
          >
            <c-icon :name="showFilters ? 'cil-chevron-top' : 'cil-chevron-bottom'"></c-icon>
          </c-button>
        </div>
      </c-card-header>
      <c-collapse :show="showFilters">
        <c-card-body class="bg-light">
          <ToolBarKpiFilter @getFilterKpi="coverageFilter" @loading="setLoading"></ToolBarKpiFilter>
        </c-card-body>
      </c-collapse>
    </c-card>

    <!-- Loading Overlay -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-content">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-2 text-primary">Loading KPI data...</p>
      </div>
    </div>

    <!-- Dashboard Content -->
    <div class="dashboard-content" :class="{ 'loading': isLoading }">
      <dashboard-wrapper :matrix="currentMatrix" :spacing="[15, 15]" :row-height="100">
        <template #one>
          <div class="chart-container coverage-chart">
            <div class="chart-header">
              <h6 class="chart-title">
                <c-icon name="cil-target" class="text-success me-2"></c-icon>
                Coverage Analysis
              </h6>
              <div class="chart-actions">
                <c-button color="ghost" size="sm" @click="refreshChart('coverage')">
                  <c-icon name="cil-reload"></c-icon>
                </c-button>
              </div>
            </div>
            <e-charts
              style="height: calc(100% - 50px)"
              autoresize
              :option="coverageOption"
              auto-resize
              class="chart-content"
            />
          </div>
        </template>
        <template #two>
          <div class="chart-container call-rate-chart">
            <div class="chart-header">
              <h6 class="chart-title">
                <c-icon name="cil-phone" class="text-info me-2"></c-icon>
                Call Rate Performance
              </h6>
              <div class="chart-actions">
                <c-button color="ghost" size="sm" @click="refreshChart('callRate')">
                  <c-icon name="cil-reload"></c-icon>
                </c-button>
              </div>
            </div>
            <e-charts
              style="height: calc(100% - 50px)"
              autoresize
              :option="callRateOption"
              auto-resize
              class="chart-content"
            />
          </div>
        </template>
        <template #three>
          <div class="chart-container frequency-advanced-chart" :class="{ 'minimized': advancedFrequencyMinimized }">
            <div class="chart-header">
              <h6 class="chart-title">
                <c-icon name="cil-chart-pie" class="text-warning me-2"></c-icon>
                Advanced Frequency
              </h6>
              <div class="chart-actions">
                <c-button color="ghost" size="sm" @click="refreshChart('frequencyAdvanced')">
                  <c-icon name="cil-reload"></c-icon>
                </c-button>
              </div>
            </div>
            <e-charts
              style="height: calc(100% - 50px)"
              autoresize
              :option="frequencyAdvancedOption"
              auto-resize
              class="chart-content"
            />
          </div>
        </template>
        <template #four>
          <div class="chart-container frequency-classic-chart">
            <div class="chart-header">
              <h6 class="chart-title">
                <c-icon name="cil-bar-chart" class="text-danger me-2"></c-icon>
                Classic Frequency
              </h6>
              <div class="chart-actions">
                <c-button color="ghost" size="sm" @click="refreshChart('frequencyClassic')">
                  <c-icon name="cil-reload"></c-icon>
                </c-button>
              </div>
            </div>
            <e-charts
              style="height: calc(100% - 50px)"
              autoresize
              :option="frequencyClassicOption"
              auto-resize
              class="chart-content"
            />
          </div>
        </template>
      </dashboard-wrapper>
    </div>
  </div>
</template>


<script>
import DashboardWrapper from '../../../components/common/dashboards/dashboardWrapper.vue';
import ToolBarKpiFilter from "./ToolBarKpiFilter.vue";

const legendAndLabel = {
  tooltip: {
    trigger: 'item'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
}

const gaugeItemStyles = [
  {
    color: '#5470C6',
    shadowColor: 'rgba(84,112,198,0.45)',
    gradient: ['#5470C6', '#91cc75']
  },
  {
    color: '#FF6B6B', // Changed Call Rate to red/coral
    shadowColor: 'rgba(255,107,107,0.5)',
    gradient: ['#FF6B6B', '#FFE66D']
  },
  {
    color: '#4ECDC4', // Changed Coverage to teal/cyan
    shadowColor: 'rgba(78,205,196,0.5)',
    gradient: ['#4ECDC4', '#44A08D']
  },
];

const barClassColors = {
  a: { primary: '#667eea', accent: '#56ccf2' },
  b: { primary: '#50a7c2', accent: '#b7f8db' },
  c: { primary: '#fcb69f', accent: '#fad0c4' },
};

const grid = {
  top: '10%',
  bottom: '10%',
  left: '5%',
  right: '5%',
  containLabel: true,
}

const media = [
  // {
  //   query: {
  //     maxWidth: 700
  //   },
  //   option: {
  //     title: [
  //       { left: "center" },
  //       { left: '50%', top: '30%', },
  //       { left: '50%', top: '60%', },
  //       { left: '50%', top: '90%', },
  //     ],
  //     series: [
  //       {
  //         center: ['50%', '20%'],
  //       },
  //       {
  //         center: ['50%', '50%'],
  //       },
  //       {
  //         center: ['50%', '80%'],
  //       },
  //     ]
  //   }
  // },
  {
    option: {
      title: [
        { left: "center", top: '5%' },
        { left: '20%', top: '30%', },
        { left: '50%', top: '30%', },
        { left: '80%', top: '30%', },
      ],
      series: [
        {
          center: ['20%', '50%'],
        },
        {
          center: ['50%', '50%'],
        },
        {
          center: ['80%', '50%'],
        },
      ]
    }
  },
];

const matrix = {
  one: { x: 0, y: 0, sizeX: 3, sizeY: 2 },
  two: { x: 3, y: 0, sizeX: 3, sizeY: 2 },
  three: { x: 0, y: 2, sizeX: 2, sizeY: 2 },
  four: { x: 2, y: 2, sizeX: 4, sizeY: 2 },
}

const matrixMinimized = {
  one: { x: 0, y: 0, sizeX: 3, sizeY: 2 },
  two: { x: 3, y: 0, sizeX: 3, sizeY: 2 },
  three: { x: 0, y: 2, sizeX: 1, sizeY: 1 }, // Minimized when only B shown
  four: { x: 1, y: 2, sizeX: 5, sizeY: 2 },
}

const matrixMobile = {
  one: { x: 0, y: 0, sizeX: 1, sizeY: 3 },
  two: { x: 0, y: 3, sizeX: 1, sizeY: 3 },
  three: { x: 0, y: 6, sizeX: 1, sizeY: 3 },
  four: { x: 0, y: 9, sizeX: 1, sizeY: 3 },
}
export default {
  components: {
    ToolBarKpiFilter,
    DashboardWrapper,
  },
  data: function () {
    return {
      coverageOption: null,
      callRateOption: null,
      frequencyClassicOption: null,
      frequencyAdvancedOption: null,
      showFilters: true,
      isLoading: false,
      lastUpdated: new Date().toLocaleTimeString(),
      windowWidth: window.innerWidth,
      advancedFrequencyMinimized: false,
    };
  },
  computed: {
    currentMatrix() {
      if (this.isMobile()) {
        return matrixMobile;
      }
      return this.advancedFrequencyMinimized ? matrixMinimized : matrix;
    }
  },
  methods: {
    isMobile() {
      return window.innerWidth <= 768;
    },
    handleResize() {
      this.windowWidth = window.innerWidth;
      // Matrix is now handled by computed property
    },
    toggleFilters() {
      this.showFilters = !this.showFilters;
    },
    setLoading(loading) {
      this.isLoading = loading;
    },
    refreshChart() {
      // Add refresh functionality for individual charts
      this.isLoading = true;
      setTimeout(() => {
        this.isLoading = false;
        this.lastUpdated = new Date().toLocaleTimeString();
      }, 1000);
    },
    prepareGauge(widgetOptionName, colorSchema, titleName, [title, body]) {
      if (!this[widgetOptionName]) {
        this[widgetOptionName] = {
          grid,
          title: [
            {
              text: titleName,
              left: 'center'
            },
            title,
          ],
          ...legendAndLabel,
          series: [
            body(colorSchema)
          ],
          media
        };
        return;
      }
      this[widgetOptionName] = {
        grid,
        title: [
          ...this[widgetOptionName].title,
          title,
        ],
        ...legendAndLabel,
        series: [
          ...this[widgetOptionName].series,
          body(colorSchema)
        ],
        media
      }
    },
    useCallRateWidget(data) {
      this.prepareGauge('callRateOption', gaugeItemStyles[1], 'Call Rate', data)
    },
    useCoverageWidget(data) {
      this.prepareGauge('coverageOption', gaugeItemStyles[2], 'Coverage', data)
    },
    setBodyTitle(name, max, value) {
      const title = {
        subtext: name,
        textAlign: 'center'
      }
      const body = (colorSchema) => ({
        type: 'gauge',
        legendHoverLink: true,
        startAngle: 180,
        endAngle: 0,
        min: 0,
        max,
        splitNumber: 10,
        radius: '50%',
        itemStyle: {
          color: colorSchema.color,
          shadowColor: colorSchema.shadowColor,
          shadowBlur: 10,
          shadowOffsetX: 2,
          shadowOffsetY: 2
        },
        progress: {
          show: true,
          roundCap: true,
          width: 18
        },
        pointer: {
          show: false,
        },
        axisLine: {
          roundCap: true,
          lineStyle: {
            width: 18,
          }
        },
        axisTick: {
          showMinLabel: true,
          showMaxLabel: true,
          splitNumber: 150,
          lineStyle: {
            width: 0,
            color: '#999'
          }
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          distance: 5,
          color: '#999',
          fontSize: 12,
          formatter: function (value) {
            if (value === max || value === 0)
              return value;
          },
        },
        title: {
          show: true,
        },
        detail: {
          color: colorSchema.color,
          height: 40,
          offsetCenter: [0, 0],
          valueAnimation: true,
        },
        data: [{ value }],
      });
      return [title, body]
    },
    * generateCoverageGaugeWidget(Coverages) {
      for (let index = 0; index < Coverages.length; index++) {
        const [doctors, coverage] = Coverages[index].data
        yield this.setBodyTitle(
          Coverages[index].name,
          doctors.value,
          coverage.value,
        )

      }
    },
    * generateCallRateGaugeWidget(CallRates) {

      for (let index = 0; index < CallRates.length; index++) {
        console.log(CallRates[index].name);
        console.log(CallRates[index]);
        const [actual, callrate] = CallRates[index].data
        yield this.setBodyTitle(
          CallRates[index].name,
          actual.value,
          callrate.value,
        )

      }
    },
    setAdvancedFrequencyWidget(data) {
      // Check if A and C are disabled (have 0 values or are missing)
      const aData = data.find(item => item.name === 'A');
      const bData = data.find(item => item.name === 'B');
      const cData = data.find(item => item.name === 'C');

      const aDisabled = !aData || aData.value === 0;
      const cDisabled = !cData || cData.value === 0;
      const bActive = bData && bData.value > 0;

      // Set minimized flag if A and C are disabled but B is active (only B shown)
      this.advancedFrequencyMinimized = aDisabled && cDisabled && bActive;

      if (this.advancedFrequencyMinimized) {
        // When minimized, create a layout with small B box on left and A,C chart on right
        this.frequencyAdvancedOption = {
          title: {
            text: "Advanced Frequency",
            left: 'center',
            textStyle: {
              fontSize: 14,
              color: '#333'
            }
          },
          graphic: [
            {
              type: 'rect',
              left: 10,
              top: 70,
              shape: {
                width: 40,
                height: 40
              },
              style: {
                fill: '#666',
                stroke: '#333',
                lineWidth: 1
              }
            },
            {
              type: 'text',
              left: 30,
              top: 90,
              style: {
                text: 'B',
                font: 'bold 12px Arial',
                fill: '#fff',
                textAlign: 'center'
              }
            }
          ],
          grid: {
            left: 65,
            right: 20,
            top: 60,
            bottom: 40,
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: ['A', 'C'],
            axisLabel: {
              fontSize: 12,
              color: '#333'
            }
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              fontSize: 12,
              color: '#333'
            }
          },
          series: [
            {
              name: 'Meet',
              type: 'bar',
              stack: 'total',
              data: [
                {
                  value: aData ? aData.meet || 301 : 301,
                  itemStyle: { color: barClassColors.a.primary }
                },
                {
                  value: cData ? cData.meet || 320 : 320,
                  itemStyle: { color: barClassColors.c.primary }
                }
              ],
              label: {
                show: true,
                position: 'inside',
                color: '#fff',
                fontSize: 11
              }
            },
            {
              name: 'Below',
              type: 'bar',
              stack: 'total',
              data: [
                {
                  value: aData ? aData.below || 90 : 90,
                  itemStyle: { color: barClassColors.a.accent }
                },
                {
                  value: cData ? cData.below || 120 : 120,
                  itemStyle: { color: barClassColors.c.accent }
                }
              ],
              label: {
                show: true,
                position: 'inside',
                color: '#fff',
                fontSize: 11
              }
            }
          ],
          legend: {
            data: ['Meet', 'Below'],
            bottom: 5,
            left: 'center',
            itemWidth: 12,
            itemHeight: 8,
            textStyle: {
              fontSize: 10
            }
          }
        }
      } else {
        // Normal display when all data is available
        this.frequencyAdvancedOption = {
          title: {
            text: "Advanced Frequency",
            left: 'center',
            textStyle: {
              fontSize: 14,
              color: '#333'
            }
          },
          grid: {
            top: '20%',
            bottom: '10%',
            left: '5%',
            right: '5%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: data.map(item => item.name),
            axisLabel: {
              fontSize: 12,
              color: '#333'
            }
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              fontSize: 12,
              color: '#333'
            }
          },
          series: [
            {
              data: data.map(item => ({
                value: item.value,
                itemStyle: {
                  color: item.name === 'A' ? barClassColors.a.primary :
                         item.name === 'B' ? barClassColors.b.primary :
                         barClassColors.c.primary
                }
              })),
              type: 'bar',
              barWidth: '50%',
              label: {
                show: true,
                position: 'top',
                fontSize: 12
              }
            }
          ]
        }
      }
    },
    setClassicFrequencyWidget() {
      this.frequencyClassicOption = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            // Use axis to trigger tooltip
            type: 'shadow' // 'shadow' as default; can also be 'line' or 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          // type: 'value'
          show: false,
        },
        yAxis: {
          type: 'category',
          data: ['C', 'B', 'A'],
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            formatter: function (value) {
              return `{label|${value}}`;
            },
            margin: 20,
            rich: {
              label: {
                height: 110,
                width: 110,
                fontSize: 18,
                fontWeight: 'bold',
                align: 'center',
                color: 'white',
                backgroundColor: 'grey'
              },
            }
          }
        },
        series: [
          {
            name: 'Meet',
            type: 'bar',
            stack: 'total',
            label: {
              show: true
            },
            emphasis: {
              focus: 'series'
            },
            data: [
              {
                value: 320,
                itemStyle: {
                  color: barClassColors.c.primary
                }
              }, {
                value: 302,
                itemStyle: {
                  color: barClassColors.b.primary
                }
              }, {
                value: 301,
                itemStyle: {
                  color: barClassColors.a.primary
                }
              }
            ]
          },
          {
            name: 'Below',
            type: 'bar',
            stack: 'total',
            label: {
              show: true
            },
            emphasis: {
              focus: 'series'
            },
            data: [
              {
                value: 120,
                itemStyle: {
                  color: barClassColors.c.accent
                }
              }, {
                value: 123,
                itemStyle: {
                  color: barClassColors.b.accent
                }
              }, {
                value: 90,
                itemStyle: {
                  color: barClassColors.a.accent
                }
              }
            ]
          }
        ]
      }
    },
    coverageFilter(argFilter) {
      this.isLoading = true;
      axios.post("/api/analyzer/kpis", argFilter)
        .then(res => {
          this.coverageOption = null;
          this.callRateOption = null;
          for (const widget of this.generateCoverageGaugeWidget(res.data.coverage)) {
            this.useCoverageWidget(widget)
          }
          for (const widget of this.generateCallRateGaugeWidget(res.data.call_rate)) {
            this.useCallRateWidget(widget)
          }

          this.setAdvancedFrequencyWidget(res.data.advancedFrequency)
          this.setClassicFrequencyWidget(res.data.classicFrequency)

          this.lastUpdated = new Date().toLocaleTimeString();
        })
        .catch(this.showErrorMessage)
        .finally(() => {
          this.isLoading = false;
        });
    }
  },
  mounted() {
    window.addEventListener('resize', this.handleResize);
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.handleResize);
  }
}
</script>

<style scoped>
@import "../../../../node_modules/@syncfusion/ej2-base/styles/material.css";
@import "../../../../node_modules/@syncfusion/ej2-inputs/styles/material.css";
@import "../../../../node_modules/@syncfusion/ej2-vue-dropdowns/styles/material.css";
@import "../../../../node_modules/@syncfusion/ej2-buttons/styles/material.css";
@import '../../../../node_modules/@syncfusion/ej2-popups/styles/material.css';
@import '../../../../node_modules/@syncfusion/ej2-lists/styles/material.css';
@import "../../../../node_modules/@syncfusion/ej2-vue-calendars/styles/material.css";

/* KPI Dashboard Styles */
.kpi-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
}

.dashboard-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.dashboard-header .card-body {
  background: transparent;
}

.dashboard-header h2 {
  color: white !important;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.filter-card {
  border: none;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.bg-gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.chart-container {
  height: 100%;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.08);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(0,0,0,0.05);
  position: relative;
}

.chart-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.chart-container:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0,0,0,0.15);
}

.chart-container:hover::before {
  opacity: 1;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid rgba(0,0,0,0.08);
  position: relative;
  overflow: hidden;
}

.chart-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.3), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.chart-container:hover .chart-header::after {
  transform: translateX(100%);
}

.chart-title {
  margin: 0;
  font-weight: 600;
  color: #495057;
  display: flex;
  align-items: center;
}

.chart-actions {
  display: flex;
  gap: 8px;
}

.chart-content {
  padding: 12px;
  min-height: 150px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Specific chart styling */
.coverage-chart .chart-header {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
}

.call-rate-chart .chart-header {
  background: linear-gradient(135deg, #cce7ff 0%, #b3d9ff 100%);
}

.frequency-advanced-chart .chart-header {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
}

.frequency-classic-chart .chart-header {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
}

/* Minimized advanced frequency styling */
.frequency-advanced-chart.minimized {
  opacity: 0.8;
  transform: scale(0.9);
  transition: all 0.3s ease;
}

.frequency-advanced-chart.minimized .chart-header {
  padding: 4px 8px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
}

.frequency-advanced-chart.minimized .chart-title {
  font-size: 0.7rem;
  font-weight: 500;
  color: #6c757d;
}

.frequency-advanced-chart.minimized .chart-content {
  padding: 4px;
  min-height: 60px;
}

.frequency-advanced-chart.minimized .chart-actions {
  display: none;
}



/* Enhanced loading overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  backdrop-filter: blur(8px);
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.loading-content {
  text-align: center;
  padding: 40px;
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0,0,0,0.15);
  animation: slideUp 0.4s ease;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dashboard-content.loading {
  opacity: 0.6;
  pointer-events: none;
  filter: blur(1px);
  transition: all 0.3s ease;
}

/* Badge styling */
.badge.bg-success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

/* Enhanced responsive design */
@media (max-width: 1200px) {
  .kpi-dashboard {
    padding: 15px;
  }

  .chart-container {
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .kpi-dashboard {
    padding: 10px;
  }

  .dashboard-header .d-flex {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .chart-header {
    padding: 10px 15px;
  }

  .chart-title {
    font-size: 0.9rem;
  }

  .chart-container {
    margin-bottom: 15px;
    border-radius: 12px;
  }

  .chart-content {
    padding: 15px 10px;
  }

  .dashboard-header h2 {
    font-size: 1.5rem;
  }

  .badge {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .kpi-dashboard {
    padding: 8px;
  }

  .dashboard-header {
    margin-bottom: 15px;
  }

  .dashboard-header h2 {
    font-size: 1.3rem;
  }

  .chart-container {
    margin-bottom: 12px;
  }

  .chart-header {
    padding: 8px 12px;
  }

  .chart-title {
    font-size: 0.85rem;
  }

  .chart-content {
    padding: 10px 8px;
  }
}

/* Animation for filter collapse */
.collapse-enter-active, .collapse-leave-active {
  transition: all 0.3s ease;
}

.collapse-enter, .collapse-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Enhanced button styling */
.btn-ghost {
  background: transparent;
  border: 1px solid rgba(0,0,0,0.1);
  transition: all 0.2s ease;
}

.btn-ghost:hover {
  background: rgba(0,0,0,0.05);
  transform: scale(1.05);
}

/* Chart content animations */
.chart-content {
  animation: fadeInUp 0.6s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Pulse animation for live data badge */
.bg-success {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
  }
  50% {
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.6);
  }
  100% {
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
  }
}

/* Smooth transitions for all interactive elements */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;
}

/* Enhanced scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8, #6a4190);
}
</style>
