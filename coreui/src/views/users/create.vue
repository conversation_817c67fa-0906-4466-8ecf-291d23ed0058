<template>
  <CRow>
    <CCol col="12" lg="12">
      <CCard no-header>
        <CCardBody>
          <template slot="header"> Create User </template>
          <CTabs>
            <CTab active>
              <template slot="title">
                <CIcon class="custom_icon" name="cil-chart-pie" /> Basic Info
              </template>
              <CCard no-header>
                <CCardBody>
                  <div class="row">
                    <div class="col">
                      <CInput label="User Name" type="text" placeholder="User Name" v-model="user.name"></CInput>
                    </div>
                    <div class="col">
                      <CInput label="Full Name" type="text" placeholder="Full Name" v-model="user.fullname"></CInput>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col">
                      <CFormGroup>
                        <template #label> Scientific Office </template>
                        <template #input>
                          <v-select label="name" v-model="user.office_id" :options="offices" placeholder="Select Office"
                            :reduce="(office) => office.id" class="mt-2" />
                        </template>
                      </CFormGroup>
                    </div>
                    <div class="col">
                      <CInput label="Work Email" type="email" placeholder="Work Email" v-model="user.email"></CInput>
                    </div>
                    <div class="col">
                      <CInput label="Personal Email" type="email" placeholder="Personal Email"
                        v-model="user.personal_email">
                      </CInput>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col">
                      <CFormGroup>
                        <template #label> Status </template>
                        <template #input>
                          <v-select title="Status" v-model="user.status" :options="status" placeholder="Select option"
                            :reduce="(status) => status.value" class="mt-2" />
                        </template>
                      </CFormGroup>
                    </div>
                    <div class="col">
                      <CFormGroup>
                        <template #label> Role </template>
                        <template #input>
                          <v-select v-model="user.menuroles" multiple :options="roles" :reduce="(role) => role"
                            placeholder="Select Role" class="mt-2" />
                        </template>
                      </CFormGroup>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col">
                      <CInput label="Password" type="password" placeholder="Password" v-model="user.password"></CInput>
                    </div>
                    <div class="col">
                      <CInputFile type="file" name="image_name" v-on:change="onImageChange" label="Profile Picture" />
                    </div>
                  </div>
                  <div class="row">
                    <div class="col-4">
                      <c-form-group>
                        <template #label>
                          <strong>Hiring Date</strong>
                        </template>
                        <template #input>
                          <c-input type="date" class="mt-2" placeholder="Date" v-model="user.hiringDate"></c-input>
                        </template>
                      </c-form-group>
                    </div>
                    <div class="col-4">
                      <c-form-group>
                        <template #label>
                          <strong>Employee Code</strong>
                        </template>
                        <template #input>
                          <c-input type="text" class="mt-2" placeholder="Code" v-model="user.emp_code"></c-input>
                        </template>
                      </c-form-group>
                    </div>
                    <div class="col-4">
                      <c-form-group>
                        <template #label>
                          <strong>Serial Device</strong>
                        </template>
                        <template #input>
                          <c-input type="text" class="mt-2" placeholder="Serial" v-model="user.serial"></c-input>
                        </template>
                      </c-form-group>
                    </div>
                  </div>
                </CCardBody>
                <CCardFooter>
                  <CButton color="primary" @click="store" style="float: right">Create</CButton>
                  <CButton color="danger" @click="$router.go(-1)" style="float: left">Cancel</CButton>
                </CCardFooter>
              </CCard>
            </CTab>

            <CTab>
              <template slot="title">
                <CIcon class="custom_icon" name="cil-chart-pie" /> Details
              </template>
              <CCard no-header>
                <CCardBody>
                  <div class="row">
                    <div class="col">
                      <CInput label="Social Security Number" type="text" placeholder="Socila Security Number"
                        v-model="userDetails.ssn"></CInput>
                    </div>
                    <div class="col">
                      <CInput label="Date Of Birth" type="date" placeholder="Date Of Birth" v-model="userDetails.dob">
                      </CInput>
                    </div>
                  </div>

                  <div class="row">
                    <!-- <div class="col">
                      <CInput label="Land Line" type="textarea" placeholder="Land Line" v-model="userDetails.tel"></CInput>
                    </div> -->
                    <div class="col">
                      <CFormGroup wrapperClasses="input-group pt-2" description="ex. 02-99999999">
                        <template #prepend-content>
                          <CIcon name="cil-phone" />
                        </template>
                        <template #label> Tel. </template>
                        <template #input>
                          <masked-input type="tel" name="tel" class="form-control" v-model="userDetails.tel" :mask="[
                            0,
                            2,
                            '-',
                            /\d/,
                            /\d/,
                            /\d/,
                            /\d/,
                            /\d/,
                            /\d/,
                            /\d/,
                            /\d/,
                          ]" :guide="true" placeholderChar="#" />
                        </template>
                      </CFormGroup>
                    </div>
                    <!-- <div class="col">
                      <CInput label="Mobile" type="text" placeholder="Mobile" v-model="userDetails.mobile"></CInput>
                    </div> -->
                    <div class="col">
                      <CFormGroup wrapperClasses="input-group pt-2" description="ex. 0 (*************">
                        <template #prepend-content>
                          <CIcon name="cil-phone" />
                        </template>
                        <template #label> Mobile </template>
                        <template #input>
                          <masked-input type="mobile" name="mobile" class="form-control" v-model="userDetails.mobile"
                            :mask="[
                              '0',
                              /[1-9]/,
                              /\d/,
                              /\d/,
                              /\d/,
                              /\d/,
                              /\d/,
                              /\d/,
                              /\d/,
                              /\d/,
                              /\d/,
                            ]" :guide="true" placeholderChar="#" />
                        </template>
                      </CFormGroup>
                    </div>
                  </div>

                  <div class="row">
                    <div class="col">
                      <CTextarea label="Address" type="text" placeholder="Address" v-model="userDetails.address">
                      </CTextarea>
                    </div>
                    <div class="col">
                      <CFormGroup>
                        <template #label> Gender </template>
                        <template #input>
                          <v-select title="Gender" v-model="userDetails.gender" :options="genders"
                            placeholder="Select option" :reduce="(gender) => gender.value" class="mt-2" />
                        </template>
                      </CFormGroup>
                    </div>
                  </div>
                </CCardBody>
                <CCardFooter>
                  <CButton color="primary" @click="storeDetails()" style="float: right">Save</CButton>
                  <CButton color="danger" @click="$router.go(-1)" style="float: left">Cancel</CButton>
                </CCardFooter>
              </CCard>
            </CTab>

            <CTab>
              <template slot="title">
                <CIcon class="custom_icon" name="cil-chart-pie" /> Line & Division
              </template>
              <CCard no-header>
                <CCardBody>
                  <line-division-tab :user_id="userDetails.user_id" />
                </CCardBody>

              </CCard>
            </CTab>

          </CTabs>
        </CCardBody>
      </CCard>
    </CCol>
  </CRow>
</template>

<script>
import MaskedInput from "vue-text-mask";
import vSelect from "vue-select";
import "vue-select/dist/vue-select.css";
import LineDivisionTab from "../../components/users/filterData.vue";

export default {
  name: "CreateUser",
  components: {
    LineDivisionTab,
    MaskedInput,
    vSelect,
  },
  data() {
    return {
      user: {
        name: "",
        fullname: "",
        office_id: null,
        email: "",
        serial: "",
        personal_email: "",
        password: "",
        status: "Active",
        menuroles: "user",
        image: null,
        hiringDate: null,
        emp_code: null
      },
      userDetails: {
        user_id: 0,
        dob: "",
        ssn: "",
        mobile: "",
        tel: "",
        address: "",
        gender: "",
      },
      offices: [],
      status: [
        { value: "Active", label: "Active" },
        { value: "Inactive", label: "Inactive" },
      ],
      genders: [
        { value: "male", label: "Male" },
        { value: "female", label: "Female" },
      ],
      roles: [],
      image_name: null,
    };
  },
  methods: {
    initialize() {
      axios
        .get("/api/users/create")
        .then((response) => {
          this.statuses = response.data.status;
          this.offices = response.data.offices;
          this.roles = Object.keys(response.data.roles).map((key) => {
            return response.data.roles[key];
          });
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    onImageChange(files, event) {
      this.user.image = event.target.files[0];
    },
    store() {
      let formData = new FormData();
      formData.append("image", this.user.image);
      formData.append("office_id", this.user.office_id);
      formData.append("name", this.user.name);
      formData.append("fullname", this.user.fullname);
      formData.append("serial", this.user.serial);
      formData.append("email", this.user.email);
      formData.append("personal_email", this.user.personal_email);
      formData.append("status", this.user.status);
      formData.append("menuroles", this.user.menuroles);
      formData.append("password", this.user.password);
      formData.append("hiringDate", this.user.hiringDate);
      formData.append("empCode", this.user.emp_code);
      axios
        .post("/api/users", formData, {
          headers: {
            "content-type": "multipart/form-data",
          },
        })
        .then((response) => {
          this.user = {
            name: "",
            fullname: "",
            email: "",
            personal_email: "",
            image: null,
            password: "",
            serial: "",
            hiringDate: "",
            emp_code: "",
          };
          this.userDetails.user_id = response.data.user_id;
          this.flash("User Created Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
    storeDetails() {
      axios
        .post("/api/user/details", {
          user_id: this.userDetails.user_id,
          dob: this.userDetails.dob,
          ssn: this.userDetails.ssn,
          mobile: this.userDetails.mobile,
          tel: this.userDetails.tel,
          address: this.userDetails.address,
          gender: this.userDetails.gender,
        })
        .then((response) => {
          this.userDetails = {
            user_id: 0,
            dob: "",
            ssn: "",
            mobile: "",
            tel: "",
            address: "",
            gender: "",
          };
          this.flash("User Details Created Successfully");
        })
        .catch((error) => {
          this.showErrorMessage(error);
        });
    },
  },
  created() {
    this.initialize();
  },
};
</script>
