<?php

namespace App\Jobs;

use App\Import;
use App\Models\TemporaryUpload;
use App\Services\Enums\TemporaryUploadCase;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class ProcessingTempUploadsJob implements ShouldQueue
{
    use Batchable, Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 60 * 30;
    public $failOnTimeout = true;
    private array $ids;
    private int $importId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(array $ids, int $importId)
    {
        $this->ids = $ids;
        $this->importId = $importId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle(): void
    {
        $query = TemporaryUpload::whereIn('uuid', $this->ids);
        $temps = (clone $query)->get();
        $query->update(['status' => TemporaryUpload::PROCESSING]);

        $temp_table = Str::random(15);

        DB::statement('CREATE TEMPORARY TABLE ' . 'crm_' . $temp_table . '(
            `uuid` char(36) COLLATE utf8mb4_unicode_ci NOT NULL,
            `errors` json DEFAULT NULL,
            `status` enum(\'pending\',\'processing\',\'finished\',\'error\') COLLATE utf8mb4_unicode_ci NOT NULL
        )');

        $firstTemp = $temps->first();
        $pluralModel = Str::plural($firstTemp->model);
        $case = TemporaryUploadCase::from($firstTemp->case);

        $import_class = Str::replaceFirst(
            Str::is("App\\Models\\*", $pluralModel) ? 'App\Models' : 'App',
            $case === TemporaryUploadCase::NORMAL ? 'App\Imports' : 'App\Imports\Updates',
            $pluralModel . 'Import'
        );

        $refinedData = [];
        $updatedData = [];
        $processedModel = $firstTemp->model;

        foreach ($temps as $key => $temp) {

            $data = collect($temp->records);

            $modelImport = new $import_class($temp->files_imported_id, $temp->model, $case, $key, true, true, $data["extra"]);

            $errors = $modelImport->collection($data);

            if (!$errors) {
                [$model, $preProcessedData] = $modelImport->getProcessedData();
                if (!isNullable($model) && $processedModel !== $model) {
                    $processedModel = $model;
                }
                $this->logger($processedModel);
                $this->mergeOrPush($refinedData, $preProcessedData);
            }

            $updatedData[] = [
                'uuid' => $temp->uuid,
                'status' => is_null($errors) ? TemporaryUpload::FINISHED : TemporaryUpload::ERROR,
                'errors' => is_null($errors) ? $errors : json_encode($errors)
            ];
        }

        DB::table($temp_table)
            ->insert($updatedData);

        DB::table('temporary_uploads')
            ->join($temp_table, 'temporary_uploads.uuid', '=', $temp_table . '.uuid')
            ->update([
                'temporary_uploads.status' => DB::raw('crm_' . $temp_table . '.status'),
                'temporary_uploads.errors' => DB::raw('crm_' . $temp_table . '.errors'),
            ]);

        if (!empty($refinedData)) {
            $this->logger($processedModel);
            $this->logger($refinedData);
            foreach(array_chunk($refinedData, 100) as $chunk) {
                resolve($processedModel)->insert($chunk);
            }

        }

        Import::where('id', $this->importId)->update([
            'save_to_date' => now()
        ]);

        DB::statement('DROP TEMPORARY TABLE IF EXISTS crm_' . $temp_table);
    }

    private function mergeOrPush(array &$originalArray, $newElement): void
    {
        $firstElementType = isset($newElement[0])
            ? gettype($newElement[0])
            : null;

        if ($firstElementType === 'array') {
            $originalArray = array_merge($originalArray, (array)$newElement);
        } else {
            if (!empty($newElement))
                $originalArray[] = $newElement;
        }
    }


    private function logger($data): void
    {
        if (config('app.debug'))
            Log::debug($data);
    }
}
